/**
 * Utility functions for Turnkey authentication data management
 */

export interface TurnkeyStoredData {
  organizationId: string;
  credentialBundle: string;
}

/**
 * Retrieves stored Turnkey authentication data from HTTP-only cookies
 * @returns Promise<TurnkeyStoredData | null> - The stored Turnkey data or null if not found
 */
export async function getTurnkeyStoredData(): Promise<TurnkeyStoredData | null> {
  try {
    const response = await fetch("/api/auth/turnkey-data", {
      cache: "no-store",
    });
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    return {
      organizationId: data.organizationId,
      credentialBundle: data.credentialBundle,
    };
  } catch (error) {
    console.error("Failed to retrieve Turnkey data:", error);
    return null;
  }
}

/**
 * Clears stored Turnkey authentication data from HTTP-only cookies
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export async function clearTurnkeyStoredData(): Promise<boolean> {
  try {
    const response = await fetch("/api/auth/turnkey-data", {
      method: "DELETE",
    });
    
    return response.ok;
  } catch (error) {
    console.error("Failed to clear Turnkey data:", error);
    return false;
  }
}
