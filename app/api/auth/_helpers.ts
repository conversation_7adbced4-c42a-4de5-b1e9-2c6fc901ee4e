import rf from "@/services/RequestFactory";
import {
  COOKIES_ACCESS_TOKEN_KEY,
  COOKIES_TURNKEY_ORG_ID_KEY,
  COOKIES_TURNKEY_CREDENTIAL_BUNDLE_KEY,
  JWT_LIVE_TIME,
} from "@/constants/common";
import { TurnkeyLoginResponse } from "@/types/turnkey.type";

export async function handleCallbackProvider(
  token: string,
  provider: string,
  email?: string | null,
  publicKey?: string
): Promise<TurnkeyLoginResponse> {
  const payload: any = {
    oauthProvider: { oidcToken: token, providerName: provider },
  };
  if (email) payload.email = email;
  if (publicKey) payload.publicKey = publicKey;

  const res: any = await rf
    .getRequest("TurnkeyAuthRequest")
    .exchangeTurnkeySession(payload);

  if (!res?.jwtToken) {
    throw new Error("Backend did not return a jwtToken");
  }

  return res;
}

export async function handleLoginSuccess(jwtToken?: string) {
  if (!jwtToken) throw new Error("Missing jwtToken");

  try {
    const { cookies } = await import("next/headers");
    const cookieStore = await cookies();
    cookieStore.set({
      name: COOKIES_ACCESS_TOKEN_KEY,
      value: jwtToken,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);
  } catch {}

  return { jwtToken };
}

export async function handleTurnkeyLoginSuccess(
  turnkeyResponse: TurnkeyLoginResponse
) {
  if (!turnkeyResponse?.jwtToken) throw new Error("Missing jwtToken");

  try {
    const { cookies } = await import("next/headers");
    const cookieStore = await cookies();

    // Store JWT token
    cookieStore.set({
      name: COOKIES_ACCESS_TOKEN_KEY,
      value: turnkeyResponse.jwtToken,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);

    // Store Turnkey organization ID
    cookieStore.set({
      name: COOKIES_TURNKEY_ORG_ID_KEY,
      value: turnkeyResponse.subOrganizationId,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);

    // Store Turnkey credential bundle
    cookieStore.set({
      name: COOKIES_TURNKEY_CREDENTIAL_BUNDLE_KEY,
      value: turnkeyResponse.credentialBundle,
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(Date.now() + JWT_LIVE_TIME),
    } as any);

    console.log("run here");
  } catch {}

  return { jwtToken: turnkeyResponse.jwtToken };
}
