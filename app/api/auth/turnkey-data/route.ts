import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { 
  COOKIES_TURNKEY_ORG_ID_KEY, 
  COOKIES_TURNKEY_CREDENTIAL_BUNDLE_KEY 
} from "@/constants/common";

export async function GET() {
  try {
    const cookieStore = await cookies();
    const organizationId = cookieStore.get(COOKIES_TURNKEY_ORG_ID_KEY)?.value;
    const credentialBundle = cookieStore.get(COOKIES_TURNKEY_CREDENTIAL_BUNDLE_KEY)?.value;
    
    if (!organizationId || !credentialBundle) {
      return NextResponse.json({ error: "Turnkey data not found" }, { status: 404 });
    }
    
    return NextResponse.json({ 
      organizationId, 
      credentialBundle 
    }, { status: 200 });
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to retrieve Turnkey data" },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  try {
    const response = NextResponse.json({ ok: true }, { status: 200 });
    
    // Clear Turnkey organization ID cookie
    response.cookies.set(COOKIES_TURNKEY_ORG_ID_KEY, "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });
    
    // Clear Turnkey credential bundle cookie
    response.cookies.set(COOKIES_TURNKEY_CREDENTIAL_BUNDLE_KEY, "", {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      expires: new Date(0),
    });
    
    return response;
  } catch (e: any) {
    return NextResponse.json(
      { error: e?.message || "Failed to clear Turnkey data" },
      { status: 500 }
    );
  }
}
